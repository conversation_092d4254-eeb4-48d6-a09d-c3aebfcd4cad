package unite.nz.hamiltonjet.controllers.weeks;

import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.SessionScoped;
import jakarta.faces.application.FacesMessage;
import jakarta.faces.context.FacesContext;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import lombok.Getter;
import lombok.Setter;
import org.jboss.logging.Logger;
import org.primefaces.event.RowEditEvent;
import unite.nz.hamiltonjet.controllers.session.SessionController;
import unite.nz.hamiltonjet.controllers.security.IdentityController;
import unite.nz.hamiltonjet.entity.enums.JetSizeKind;
import unite.nz.hamiltonjet.entity.enums.PeriodType;
import unite.nz.hamiltonjet.entity.period.PeriodConfiguration;
import unite.nz.hamiltonjet.entity.period.PeriodSegment;
import unite.nz.hamiltonjet.service.periods.PeriodConfigurationService;
import unite.nz.hamiltonjet.utils.FiscalWeekCalculator;
import unite.nz.hamiltonjet.utils.SerialVersionGenerator;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.temporal.WeekFields;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Controller for managing period configurations and segments
 */
@Named
@SessionScoped
public class PeriodConfigurationController implements Serializable {

    @Serial
    private static final long serialVersionUID = SerialVersionGenerator.get();

    @Inject
    Logger logger;

    @Inject
    PeriodConfigurationService periodConfigurationService;

    @Inject
    SessionController sessionController;

    @Inject
    IdentityController identityController;

    @Getter
    @Setter
    private PeriodConfiguration currentConfiguration;

    @Getter
    @Setter
    private PeriodSegment newSegment;

    @Getter
    @Setter
    private List<PeriodSegment> segments;

    private Set<Long> editingSegmentIds = new HashSet<>();

    @PostConstruct
    public void init() {
        logger.debug(">init PeriodConfigurationController");
        loadConfiguration();
        initNewSegment();
        logger.debug("<init PeriodConfigurationController");
    }

    public void loadConfiguration() {
        JetSizeKind jetSizeKind = sessionController.getJetViewMode();
        currentConfiguration = periodConfigurationService.getOrCreateConfiguration(jetSizeKind);
        loadSegments();
    }

    public void loadSegments() {
        logger.debug("=== loadSegments triggered ===");
        if (currentConfiguration != null) {
            segments = periodConfigurationService.getSegments(currentConfiguration.getJetSizeKind());
        }
    }

    public void initNewSegment() {
        newSegment = new PeriodSegment();
        newSegment.setJetSizeKind(sessionController.getJetViewMode());
        newSegment.setPeriodType(PeriodType.WEEK);
        newSegment.setActive(true);
        // Order will be auto-calculated based on start date
    }

    public void saveConfiguration() {
        try {
            try {
                currentConfiguration.setUpdatedBy(identityController.getUsername());
            } catch (Exception e) {
                currentConfiguration.setUpdatedBy("system");
            }
            periodConfigurationService.saveConfiguration(currentConfiguration);
            logger.infof("Saved configuration for %s jets", currentConfiguration.getJetSizeKind());
            // Show success message
        } catch (Exception e) {
            logger.errorf("Error saving configuration: %s", e.getMessage());
            // Show error message
        }
    }

    public void addSegment() {
        try {
            // Validate the new segment
            if (newSegment.getStartDate() == null || newSegment.getEndDate() == null) {
                logger.warn("Start date and end date are required");
                return;
            }

            if (newSegment.getPeriodType() == PeriodType.MULTI_WEEK && 
                (newSegment.getWeeksCount() == null || newSegment.getWeeksCount() < 1)) {
                logger.warn("Multi-week periods require a valid weeks count");
                return;
            }

            // Check for overlapping segments
            List<PeriodSegment> overlapping = periodConfigurationService.findOverlappingSegments(
                newSegment.getJetSizeKind(), 
                newSegment.getStartDate(), 
                newSegment.getEndDate(), 
                null);

            if (!overlapping.isEmpty()) {
                logger.warnf("Segment overlaps with existing segments: %s", overlapping);
                return;
            }

            // Validate no overlaps with existing segments
            if (hasOverlapWithExistingSegments(newSegment)) {
                FacesContext.getCurrentInstance().addMessage(null,
                    new FacesMessage(FacesMessage.SEVERITY_ERROR, "Overlap Error",
                        "This segment overlaps with an existing segment. Overlapping segments are not allowed."));
                return;
            }

            // Set order based on chronological position
            newSegment.setSegmentOrder(calculateChronologicalOrder(newSegment.getStartDate()));

            periodConfigurationService.saveSegment(newSegment);
            logger.infof("Added new segment: %s to %s", newSegment.getStartDate(), newSegment.getEndDate());
            
            loadSegments();
            initNewSegment();
            
        } catch (Exception e) {
            logger.errorf("Error adding segment: %s", e.getMessage());
        }
    }

    public void deleteSegment(PeriodSegment segment) {
        try {
            periodConfigurationService.deleteSegment(segment.getId());
            logger.infof("Deleted segment %d", segment.getId());
            loadSegments();
        } catch (Exception e) {
            logger.errorf("Error deleting segment: %s", e.getMessage());
        }
    }

    public void editSegment(PeriodSegment segment) {
        //TODO implement it
        newSegment = segment;
        logger.infof("Editing segment %d", segment.getId());
    }

    // Method to check if segment is being edited
    public boolean isSegmentEditing(PeriodSegment segment) {
        return editingSegmentIds.contains(segment.getId());
    }

    // Add row edit event listeners
    public void onRowEdit(RowEditEvent<PeriodSegment> event) {
        logger.infof("=== onRowEdit triggered ===");
        try {
            PeriodSegment editedSegment = event.getObject();
            logger.debugf("onRowEdit: %s", editedSegment.getId());

            periodConfigurationService.saveSegment(editedSegment);
            logger.infof("Updated segment %d", editedSegment.getId());
            loadSegments();
            editingSegmentIds.remove(editedSegment.getId());

            FacesContext.getCurrentInstance().addMessage(null,
                new FacesMessage(FacesMessage.SEVERITY_INFO, "Success", "Segment updated successfully"));
        } catch (Exception e) {
            logger.errorf("Error updating segment: %s", e.getMessage());
            FacesContext.getCurrentInstance().addMessage(null,
                new FacesMessage(FacesMessage.SEVERITY_ERROR, "Error", "Failed to update segment"));
        }
    }
    public void onRowCancel(RowEditEvent<PeriodSegment> event) {
        logger.infof("=== onRowCancel triggered ===");

        PeriodSegment segment = event.getObject();
        logger.infof("onRowCancel: %s", segment.getId());
        editingSegmentIds.remove(segment.getId());
        // Reload segments to reflect any changes made before editing
        loadSegments();
    }
    public void onRowEditInit(RowEditEvent<PeriodSegment> event) {
        logger.infof("=== onRowEditInit triggered ===");

        PeriodSegment segment = event.getObject();
        logger.debugf("onRowEditInit: %s", segment.getId());
        editingSegmentIds.add(segment.getId());
    }

    public void testAjax() {
        logger.infof("=== Test AJAX method called ===");
        FacesContext.getCurrentInstance().addMessage(null,
            new FacesMessage(FacesMessage.SEVERITY_INFO, "Success", "Test AJAX is working!"));
    }

    // Alternative method signature like Matrix controller
    public void onEdit(PeriodSegment segment) {
        logger.infof("=== onEdit (Matrix-style) triggered ===");
        try {
            logger.debugf("onEdit: %s", segment.getId());
            periodConfigurationService.saveSegment(segment);
            logger.infof("Updated segment %d", segment.getId());
            loadSegments();

            FacesContext.getCurrentInstance().addMessage(null,
                new FacesMessage(FacesMessage.SEVERITY_INFO, "Success", "Segment updated successfully"));
        } catch (Exception e) {
            logger.errorf("Error updating segment: %s", e.getMessage());
            FacesContext.getCurrentInstance().addMessage(null,
                new FacesMessage(FacesMessage.SEVERITY_ERROR, "Error", "Failed to update segment"));
        }
    }

    /**
     * Check if the new segment overlaps with any existing segments
     */
    private boolean hasOverlapWithExistingSegments(PeriodSegment newSegment) {
        if (newSegment.getStartDate() == null || newSegment.getEndDate() == null) {
            return false; // Can't check overlap without dates
        }

        List<PeriodSegment> existingSegments = periodConfigurationService.getActiveSegments(newSegment.getJetSizeKind());

        for (PeriodSegment existing : existingSegments) {
            // Skip if it's the same segment (for updates)
            if (existing.getId() != null && existing.getId().equals(newSegment.getId())) {
                continue;
            }

            // Check for overlap: segments overlap if one starts before the other ends
            boolean overlaps = !newSegment.getEndDate().isBefore(existing.getStartDate()) &&
                              !newSegment.getStartDate().isAfter(existing.getEndDate());

            if (overlaps) {
                return true;
            }
        }

        return false;
    }

    /**
     * Calculate chronological order based on start date
     * Uses epoch days to ensure chronological ordering
     */
    public int calculateChronologicalOrder(LocalDate startDate) {
        if (startDate == null) {
            return Integer.MAX_VALUE; // Put at end if no date
        }

        // Use days since epoch as order (earlier dates = lower numbers)
        return (int) startDate.toEpochDay();
    }

    public PeriodType[] getPeriodTypes() {
        return PeriodType.values();
    }

    public JetSizeKind[] getJetSizeKinds() {
        return new JetSizeKind[]{JetSizeKind.LARGE, JetSizeKind.SMALL};
    }

    public void onJetSizeChange() {
        loadConfiguration();
        initNewSegment();
    }

    public String getSegmentDisplayName(PeriodSegment segment) {
        String typeName = segment.getPeriodType().getDisplayName();
        if (segment.getPeriodType() == PeriodType.MULTI_WEEK && segment.getWeeksCount() != null) {
            typeName += " (" + segment.getWeeksCount() + " weeks)";
        }
        return typeName;
    }

    public int getSegmentOrder(PeriodSegment segment) {
        return segment.getSegmentOrder();
    }

    public boolean isMultiWeekType() {
        return newSegment != null && newSegment.getPeriodType() == PeriodType.MULTI_WEEK;
    }

    public boolean isMultiWeekSegment(PeriodSegment segment) {
        return segment.getPeriodType() == PeriodType.MULTI_WEEK;
    }

    public String getStatusIcon(PeriodSegment segment) {
        return segment.isActive() ? "pi pi-check-circle" : "pi pi-times-circle";
    }

    public String getStatusClass(PeriodSegment segment) {
        return segment.isActive() ? "status-active" : "status-inactive";
    }

    public int getSegmentFiscalWeek(LocalDate date) {
        return FiscalWeekCalculator.getFiscalWeekNumber(date);
    }
    public Integer getSegmentWeekNumberInYear(LocalDate date) {
        // Starts on a Monday
        return date.get(WeekFields.ISO.weekOfYear());
    }
}
