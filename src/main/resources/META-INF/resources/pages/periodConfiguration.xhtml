<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core" xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:p="http://primefaces.org/ui"
				template="../template.xhtml">

	<ui:define name="title">Unite - Period Configuration</ui:define>

	<ui:define name="content">

		<h:form id="configForm">

			<div class="ui-g">

				<!-- Configuration Settings -->
				<div class="ui-g-12 ui-md-12 ui-lg-12">
					<p:card id="configCard">
						<f:facet name="title">
							<i class="pi pi-cog" style="font-size: 1.5rem"></i>
							&#160;
							Period Configuration (#{sessionController.jetViewMode})
						</f:facet>

						<div class="p-grid">
							<div class="p-col-12 p-md-4">
								<p:outputLabel for="jetSizeKind" value="Jet Size:" />
								<p:selectOneMenu id="jetSizeKind" value="#{sessionController.jetViewMode}" 
												converter="jetSizeKindConverter">
									<f:selectItems value="#{periodConfigurationController.jetSizeKinds}" 
												  var="jetSize" itemLabel="#{jetSize}" itemValue="#{jetSize}"/>
									<p:ajax listener="#{periodConfigurationController.onJetSizeChange}" 
											update="configForm"/>
								</p:selectOneMenu>
							</div>
							<div class="p-col-12 p-md-4">
								<p:outputLabel for="defaultPeriodType" value="Default Period Type:" />
								<p:selectOneMenu id="defaultPeriodType" 
												value="#{periodConfigurationController.currentConfiguration.defaultPeriodType}">
									<f:selectItems value="#{periodConfigurationController.periodTypes}" 
												  var="periodType" itemLabel="#{periodType.displayName}" itemValue="#{periodType}"/>
								</p:selectOneMenu>
							</div>
							<div class="p-col-12 p-md-4">
								<p:outputLabel for="defaultWeeksCount" value="Default Weeks (for Multi-Week):" />
								<p:inputNumber id="defaultWeeksCount" 
											  value="#{periodConfigurationController.currentConfiguration.defaultWeeksCount}"
											  minValue="1" maxValue="52" decimalPlaces="0"/>
							</div>
						</div>

						<div class="p-grid">
							<div class="p-col-12 p-md-6">
								<p:commandButton value="Save Configuration"
												action="#{periodConfigurationController.saveConfiguration}"
												update="configForm"
												icon="pi pi-save"
												styleClass="ui-button-success"/>
							</div>
						</div>
					</p:card>
				</div>

				<!-- Add New Segment -->
				<div class="ui-g-12 ui-md-12 ui-lg-12">
					<p:card id="addSegmentCard">
						<f:facet name="title">
							<i class="pi pi-plus" style="font-size: 1.5rem"></i>
							&#160;
							Add Period Segment
						</f:facet>

						<div class="p-grid">
							<div class="p-col-12 p-md-3">
								<p:outputLabel for="startDate" value="Start Date:" />
								<p:calendar id="startDate" value="#{periodConfigurationController.newSegment.startDate}" 
										   pattern="dd/MM/yyyy" showOn="button"/>
							</div>
							<div class="p-col-12 p-md-3">
								<p:outputLabel for="endDate" value="End Date:" />
								<p:calendar id="endDate" value="#{periodConfigurationController.newSegment.endDate}" 
										   pattern="dd/MM/yyyy" showOn="button"/>
							</div>
							<div class="p-col-12 p-md-3">
								<p:outputLabel for="periodType" value="Period Type:" />
								<p:selectOneMenu id="periodType" value="#{periodConfigurationController.newSegment.periodType}">
									<f:selectItems value="#{periodConfigurationController.periodTypes}" 
												  var="periodType" itemLabel="#{periodType.displayName}" itemValue="#{periodType}"/>
									<p:ajax update="weeksCountPanel"/>
								</p:selectOneMenu>
							</div>
							<div class="p-col-12 p-md-3">
								<p:outputPanel id="weeksCountPanel">
									<p:outputLabel for="weeksCount" value="Weeks Count:" 
												  rendered="#{periodConfigurationController.isMultiWeekType()}"/>
									<p:inputNumber id="weeksCount" 
												  value="#{periodConfigurationController.newSegment.weeksCount}"
												  minValue="1" maxValue="52" decimalPlaces="0"
												  rendered="#{periodConfigurationController.isMultiWeekType()}"/>
								</p:outputPanel>
							</div>
						</div>

						<div class="p-grid">
							<div class="p-col-12 p-md-6">
								<p:outputLabel for="segmentName" value="Segment Name (Optional):" />
								<p:inputText id="segmentName" value="#{periodConfigurationController.newSegment.segmentName}" 
										    style="width: 100%"/>
							</div>
							<div class="p-col-12 p-md-3">
								<p:outputLabel value="Order: (Auto-calculated)" />
								<h:outputText value="Auto" style="font-style: italic; color: #666;"/>
							</div>
							<div class="p-col-12 p-md-3">
								<br/>
								<p:commandButton value="Add Segment" 
												action="#{periodConfigurationController.addSegment}"
												update="configForm" 
												icon="pi pi-plus"
												styleClass="ui-button-success"/>
							</div>
						</div>
					</p:card>
				</div>

				<!-- Existing Segments -->
				<div class="ui-g-12 ui-md-12 ui-lg-12">
					<p:card id="segmentsCard">
						<f:facet name="title">
							<i class="pi pi-list" style="font-size: 1.5rem"></i>
							&#160;
							Period Segments
						</f:facet>

						<p:dataTable id="segmentsTable" value="#{periodConfigurationController.segments}"
									var="segment" emptyMessage="No segments configured"
									editable="true" stripedRows="true">

							<p:ajax event="rowEdit" listener="#{periodConfigurationController.onRowEdit}"
									update="@this"
									onstart="console.log('ProcessShow-style rowEdit AJAX starting')"
									oncomplete="console.log('ProcessShow-style rowEdit AJAX completed')"/>
							<p:ajax event="rowEditCancel" listener="#{periodConfigurationController.onRowCancel}"
									onstart="console.log('ProcessShow-style rowEditCancel AJAX starting')"
									oncomplete="console.log('ProcessShow-style rowEditCancel AJAX completed')"/>
							
							<p:column headerText="Name">
<!--								<p:outputPanel>-->
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{segment.segmentName}"
														  rendered="#{not empty segment.segmentName}"/>
											<h:outputText value="Unnamed Segment"
														  rendered="#{empty segment.segmentName}"
														  style="font-style: italic; color: #666;"/>
										</f:facet>
										<f:facet name="input">
											<p:inputText value="#{segment.segmentName}" style="width: 100%"/>
										</f:facet>
									</p:cellEditor>
<!--								</p:outputPanel>-->
							</p:column>

							<p:column headerText="Week / Fiscal Week Range">
								<h:outputText class="p-d-inline-flex p-ai-center padding-left-10" value="#{periodConfigurationController.getSegmentWeekNumberInYear(segment.startDate)} / #{periodConfigurationController.getSegmentFiscalWeek(segment.startDate)}"/>
								<h:outputText value=" — "/>
								<h:outputText class="p-d-inline-flex p-ai-center padding-left-10" value="#{periodConfigurationController.getSegmentWeekNumberInYear(segment.endDate)} / #{periodConfigurationController.getSegmentFiscalWeek(segment.endDate)}"/>
							</p:column>

							<p:column headerText="Date Range" style="min-width: 280px;">
								<p:cellEditor>
									<f:facet name="output">
										<h:outputText value="#{segment.startDate}">
											<f:convertDateTime type="localDate" pattern="dd/MM/yyyy"/>
										</h:outputText>
										<h:outputText value=" — "/>
										<h:outputText value="#{segment.endDate}">
											<f:convertDateTime type="localDate" pattern="dd/MM/yyyy"/>
										</h:outputText>
									</f:facet>
									<f:facet name="input">
										<p:calendar value="#{segment.startDate}" pattern="dd/MM/yyyy"
													showOn="button" style="width: 110px; display: inline-block;"/>
										<span style="display: inline-block; padding: 0 5px; vertical-align: top;"> — </span>
										<p:calendar value="#{segment.endDate}" pattern="dd/MM/yyyy"
													showOn="button" style="width: 110px; display: inline-block;"/>
									</f:facet>
								</p:cellEditor>
							</p:column>

							<p:column headerText="Period Type">
								<h:outputText value="#{periodConfigurationController.getSegmentDisplayName(segment)}"/>
								</p:column>

							<!-- Row Editor Column (like processShow.xhtml) -->
							<p:column headerText="Edit" style="width:80px">
								<p:rowEditor editTitle="Edit Row" cancelTitle="Cancel Edit" saveTitle="Save Row"/>
							</p:column>

							<!-- Delete Column -->
							<p:column headerText="Delete" style="width:80px">
								<p:commandButton icon="pi pi-trash"
												 action="#{periodConfigurationController.deleteSegment(segment)}"
												 update="segmentsTable"
												 title="Delete Segment"
												 styleClass="ui-button-danger ui-button-sm">
									<p:confirm header="Confirm Delete"
											   message="Are you sure you want to delete this segment?"
											   icon="pi pi-exclamation-triangle"/>
								</p:commandButton>
							</p:column>
						</p:dataTable>

						<p:confirmDialog global="true" showEffect="fade" hideEffect="fade" responsive="true" width="350">
							<p:commandButton value="No" type="button" styleClass="ui-confirmdialog-no ui-button-primary" icon="pi pi-times"/>
							<p:commandButton value="Yes" type="button" styleClass="ui-confirmdialog-yes ui-button-flat" icon="pi pi-check"/>
						</p:confirmDialog>
					</p:card>
				</div>

			</div>

		</h:form>

	</ui:define>

	<ui:define name="scripts">
		<script type="text/javascript">
			// Debug row editing
			$(document).ready(function() {
				console.log('Document ready - checking for row editor buttons');

				// Add click handlers to all edit buttons
				$(document).on('click', '.ui-row-editor-pencil', function() {
					console.log('Edit button clicked!');
				});

				$(document).on('click', '.ui-row-editor-check', function() {
					console.log('Save button clicked!');
				});

				$(document).on('click', '.ui-row-editor-close', function() {
					console.log('Cancel button clicked!');
				});
			});
		</script>
	</ui:define>

</ui:composition>
