<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core" xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:p="http://primefaces.org/ui"
				template="../template.xhtml">

	<ui:define name="title">Unite - Matrix Management</ui:define>

	<ui:define name="content">

		<h:form id="matrixForm">

			<div class="ui-g">

				<div class="ui-g-12 ui-md-12 ui-lg-12">

					<p:card id="matrixCard">

						<f:facet name="title">
							<i class="pi pi-hashtag" style="font-size: 1.5rem"></i>
							&#160;
							Matrix
						</f:facet>

						<br/>
						<br/>

						<p:commandButton value="XLS" styleClass="p-mr-2 p-mb-2">
							<p:dataExporter type="xls" target="matrixTable" fileName="matrix"/>
						</p:commandButton>

						<p:commandButton value="XLXS" styleClass="p-mr-2 p-mb-2">
							<p:dataExporter type="xlsxstream" target="matrixTable" fileName="matrix"/>
						</p:commandButton>
						<p:commandButton value="CSV" styleClass="p-mr-2 p-mb-2">
							<p:dataExporter type="csv" target="matrixTable" fileName="matrix"/>
						</p:commandButton>

						<p:commandButton value="XML" styleClass="p-mr-2 p-mb-2">
							<p:dataExporter type="xml" target="matrixTable" fileName="matrix"/>
						</p:commandButton>

						<p:commandButton value="Force Matrix reload" rendered="#{sessionController.isGodMode()}"
										 styleClass="ui-button-danger p-mr-2 p-mb-2"
										 update="matrixTable"
										 title="Force reload DB: Matrix. Clear current data and reload the fresh one from DB."
										 actionListener="#{matrixController.forceUpdate()}"/>


						<p:dataTable id="matrixTable" var="eachMatrix" value="#{matrixController.matrixs}"
									 editable="true" style="margin-bottom:200px" reflow="true" stripedRows="true">

							<p:ajax event="rowEdit" listener="#{matrixController.onEdit(eachMatrix)}"/>

							<f:facet name="header">
								<div class="p-d-flex p-jc-between p-ai-center">
									Orders
									<div>
										<p:commandButton id="toggler" type="button" value="Columns"
														 icon="pi pi-align-justify"/>
										<p:columnToggler datasource="matrixTable" trigger="toggler"/>
									</div>
								</div>
							</f:facet>

							<p:column headerText="Jet" visible="false">
								<h:outputText value="#{eachMatrix.id}"/>
							</p:column>

							<p:column headerText="Jet">
								<h:outputText value="#{eachMatrix.jet}"/>
							</p:column>

							<p:column headerText="Jet Name">
								<h:outputText value="#{eachMatrix.jetHumanName}"/>
							</p:column>

							<p:column headerText="Jet Size">
								<h:outputText value="#{eachMatrix.jetSizeKind}"/>
							</p:column>

							<p:column headerText="Basket points">

								<p:outputPanel>
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{eachMatrix.basketPoints}"/>
										</f:facet>

										<f:facet name="input">
											<p:inputText value="#{eachMatrix.basketPoints}"/>
										</f:facet>
									</p:cellEditor>
								</p:outputPanel>
							</p:column>

							<p:column headerText="edit">
								<p:rowEditor editTitle="Edit Row" cancelTitle="Cancel Edit" saveTitle="Save Row"/>
							</p:column>

							<p:column headerText="Max Jets Per Period">
								<p:outputPanel>
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{eachMatrix.maxJetsPerPeriod}"/>
										</f:facet>

										<f:facet name="input">
											<p:inputText value="#{eachMatrix.maxJetsPerPeriod}"/>
										</f:facet>
									</p:cellEditor>
								</p:outputPanel>
							</p:column>

							<p:column headerText="Reverse Fabricated" exportValue="#{eachMatrix.reverseFabricated}">
								<p:outputPanel rendered="#{eachMatrix.reverseFabricated}"><i style="color:green"
																							 class="bold pi pi-check"></i></p:outputPanel>
								<p:outputPanel rendered="#{!eachMatrix.reverseFabricated}"><i style="color:red"
																							  class="bold pi pi-times"></i></p:outputPanel>
							</p:column>

							<p:column headerText="Product number">
								<p:outputPanel>
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{eachMatrix.productNumber}"/>
										</f:facet>

										<f:facet name="input">
											<p:inputText value="#{eachMatrix.productNumber}"/>
										</f:facet>
									</p:cellEditor>
								</p:outputPanel>
							</p:column>

							<p:column headerText="Intake Or Transition">
								<h:outputText value="#{eachMatrix.intakeOrTransition}"/>
							</p:column>

							<p:column headerText="Transition Or Intake Manufacture Kind">
								<h:outputText value="#{eachMatrix.transitionIntakeManufactureKind}"/>
							</p:column>

							<p:column headerText="Impeller Feature #">
								<p:outputPanel>
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{eachMatrix.impellerM3FeatureNumber}"/>
										</f:facet>

										<f:facet name="input">
											<p:inputText value="#{eachMatrix.impellerM3FeatureNumber}"/>
										</f:facet>
									</p:cellEditor>
								</p:outputPanel>
							</p:column>

							<p:column headerText="Coupling Feature #">
								<p:outputPanel>
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{eachMatrix.couplingM3FeatureNumber}"/>
										</f:facet>

										<f:facet name="input">
											<p:inputText value="#{eachMatrix.couplingM3FeatureNumber}"/>
										</f:facet>
									</p:cellEditor>
								</p:outputPanel>
							</p:column>

							<p:column headerText="Controls Feature #">

								<p:outputPanel>
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{eachMatrix.controlsM3FeatureNumber}"/>
										</f:facet>

										<f:facet name="input">
											<p:inputText value="#{eachMatrix.controlsM3FeatureNumber}"/>
										</f:facet>
									</p:cellEditor>
								</p:outputPanel>
							</p:column>

							<p:column headerText="Certs Feature #">
								<p:outputPanel>
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{eachMatrix.certM3FeatureNumber}"/>
										</f:facet>

										<f:facet name="input">
											<p:inputText value="#{eachMatrix.certM3FeatureNumber}"/>
										</f:facet>
									</p:cellEditor>
								</p:outputPanel>
							</p:column>

							<p:column headerText="Jets Feature #">
								<p:outputPanel>
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{eachMatrix.jetsM3FeatureNumber}"/>
										</f:facet>

										<f:facet name="input">
											<p:inputText value="#{eachMatrix.jetsM3FeatureNumber}"/>
										</f:facet>
									</p:cellEditor>
								</p:outputPanel>
							</p:column>

							<p:column headerText="Stations Feature #">
								<p:outputPanel>
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{eachMatrix.stationsM3FeatureNumber}"/>
										</f:facet>

										<f:facet name="input">
											<p:inputText value="#{eachMatrix.stationsM3FeatureNumber}"/>
										</f:facet>
									</p:cellEditor>
								</p:outputPanel>
							</p:column>

							<p:column headerText="Paint Feature #">
								<p:outputPanel>
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{eachMatrix.paintTypeM3FeatureNumber}"/>
										</f:facet>

										<f:facet name="input">
											<p:inputText value="#{eachMatrix.paintTypeM3FeatureNumber}"/>
										</f:facet>
									</p:cellEditor>
								</p:outputPanel>
							</p:column>

							<p:column headerText="Materiall Feature #">
								<p:outputPanel>
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{eachMatrix.materialM3FeatureNumber}"/>
										</f:facet>

										<f:facet name="input">
											<p:inputText value="#{eachMatrix.materialM3FeatureNumber}"/>
										</f:facet>
									</p:cellEditor>
								</p:outputPanel>
							</p:column>

							<p:column headerText="Notes" style="width: 300px">
								<p:outputPanel>
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{eachMatrix.notes}"/>
										</f:facet>

										<f:facet name="input">
											<p:inputText value="#{eachMatrix.notes}"/>
										</f:facet>
									</p:cellEditor>
								</p:outputPanel>
							</p:column>

						</p:dataTable>

					</p:card>

					<br/>

				</div>

			</div>

		</h:form>

	</ui:define>

</ui:composition>