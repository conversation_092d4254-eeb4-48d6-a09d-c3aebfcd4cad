<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:p="http://primefaces.org/ui"
	template="../template.xhtml">	

	<ui:define name="title">Processes</ui:define>

	<ui:define name="content">
		<style>
			.custom-timeline {
				position: relative;
				padding: 20px 0;
			}

			/* Main vertical timeline line - starts from first dot and ends at last dot */
			.custom-timeline::before {
				content: '';
				position: absolute;
				left: 50%;
				width: 2px;
				background: #dee2e6;
				transform: translateX(-50%);
				z-index: 0;
				/* Calculate precise start and end points */
				top: calc(20px + 30px + 6px);    /* container padding + step margin + dot center */
				bottom: calc(20px + 30px + 20px); /* container padding + step margin + step name height */
			}

			/* Add this rule to hide the line below the last dot */
			.timeline-step:last-child::after {
				content: '';
				position: absolute;
				left: 50%;
				top: 26px; /* Start right after the dot (dot top + dot radius - plus some additional offset for margins, padding, or other layout factors (14px)) */
				width: 4px; /* Slightly wider than line to ensure coverage */
				background: white;
				transform: translateX(-50%);
				z-index: 3; /* Higher than the line z-index */
				bottom: 0; /* Cover everything below the dot */
			}

			.timeline-step {
				position: relative;
				margin: 30px 0;
				display: flex;
				flex-direction: column;
				align-items: center;
				min-height: 60px;
			}

			/* Timeline dots - positioned ON the central line */
			.timeline-step::before {
				content: '';
				position: absolute;
				left: 50%;
				top: 6px; /* Position dot at top of step container */
				width: 12px;
				height: 12px;
				background: white;
				border: 3px solid #007bff;
				border-radius: 50%;
				transform: translateX(-50%);
				z-index: 2;
				box-shadow: 0 0 0 2px #dee2e6;
			}

			/* Right-side steps (odd numbers) */
			.chronoline-step-right {
				align-items: flex-start;
				padding-left: calc(50% + 25px);
			}

			/* Left-side steps (even numbers) */
			.chronoline-step-left {
				align-items: flex-end;
				padding-right: calc(50% + 25px);
			}

			/* Step name positioning */
			.chronoline-step-right .step-name {
				text-align: left;
				width: 100%;
			}

			.chronoline-step-left .step-name {
				text-align: right;
				width: 100%;
			}

			/* Checkbox container - appears on new line under step name */
			.checkbox-container {
				margin-top: 5px;
				width: 100%;
			}

			.chronoline-step-right .checkbox-container {
				text-align: left;
				padding-left: 20px; /* Indent for odd steps */
			}

			.chronoline-step-right .checkbox-container .ui-selectbooleancheckbox {
				margin: 2px 0; /* Reduced gap */
				display: block;
			}

			.chronoline-step-left .checkbox-container {
				display: flex;
				flex-direction: column;
				align-items: flex-end;
				padding-right: 20px; /* Some spacing from right edge */
				width: 100%;
			}


			.chronoline-step-left .checkbox-container .ui-selectbooleancheckbox {
				display: flex;
				justify-content: flex-end;
				align-items: center;
				margin: 3px 0;
				direction: rtl; /* Right-to-left direction puts checkbox after text */
				text-align: right;
				width: auto;	/* Let each checkbox determine its own width */
			}

			.chronoline-step-left .checkbox-container .ui-selectbooleancheckbox .ui-chkbox {
				order: 2; /* Checkbox appears after label */
				margin-left: 8px;
			}

			.chronoline-step-left .checkbox-container .ui-selectbooleancheckbox .ui-chkbox-label {
				order: 1; /* Label appears before checkbox */
			}

			.step-name h4 {
				margin: 0 0 10px 0;
				color: #333;
			}
		</style>

		<script type="text/javascript">
			var dialogKeyHandler = null;

			function bindDialogKeyEvents(dialogWidgetVar, confirmButtonClass, cancelButtonClass) {
				dialogKeyHandler = function(event) {
					if (event.keyCode === 13) { // Enter key
						event.preventDefault();
						event.stopPropagation();
						// Get the currently visible dialog and find the confirm button within it
						var dialog = PF(dialogWidgetVar).jq[0];
						dialog.querySelector('.' + confirmButtonClass).click();
					} else if (event.keyCode === 27) { // Escape key
						event.preventDefault();
						event.stopPropagation();
						// Get the currently visible dialog and find the cancel button within it
						var dialog = PF(dialogWidgetVar).jq[0];
						dialog.querySelector('.' + cancelButtonClass).click();
					}
				};
				document.addEventListener('keydown', dialogKeyHandler);
			}

			function unbindDialogKeyEvents() {
				if (dialogKeyHandler) {
					document.removeEventListener('keydown', dialogKeyHandler);
					dialogKeyHandler = null;
				}
			}
		</script>

		<h:form id="processShowForm">

			<div class="p-grid">
	
				<div class="p-col-12 p-md-12 p-lg-12">

					<p:card id="processShowCard">
					
						<f:facet name="title">
							<p:outputLabel value="Hamilton Jet Processes"/>
						</f:facet>

						<f:subview id="buttonsGroup">
						<p:commandButton value="Force Proceses reload" rendered="#{sessionController.isGodMode()}"
										 styleClass="ui-button-danger p-mr-2 p-mb-2"
										 update="@form:processTableAndTimelinePanel:processTablePanel:processTable"
										 title="Force reload DB: Process. Clear current data and reload the fresh one from DB."
										 actionListener="#{processesController.forceUpdate()}"/>

						<!-- Hidden refresh button that can be triggered programmatically -->
						<p:commandButton id="hiddenRefreshButton"
										 value="Refresh Table"
										 style="display: none;"
										 update="@form:processTableAndTimelinePanel"
										 actionListener="#{processesController.refreshTableData()}"/>

						<!-- Remote command for programmatic refresh -->
						<p:remoteCommand name="refreshTableRemoteCommand"
										 actionListener="#{processesController.refreshTableData()}"
										 update="@form:processTableAndTimelinePanel" />

						<!-- Jet Size Switch Button -->
						<p:selectOneButton unselectable="false" value="#{processesController.selectedJetSizeKind}"
										   style="border: 1px solid white; border-radius: 5px; float: right !important;"
										   styleClass="jetSwitchButton">
							<f:selectItem itemLabel="Small" itemValue="SMALL"/>
							<f:selectItem itemLabel="Large" itemValue="LARGE"/>
							<p:ajax update="@form:processShowCard" listener="#{processesController.jetSizeChangeListener}"/>
						</p:selectOneButton>
						</f:subview>

						<p:outputPanel id="processChoicePanel" styleClass="ui-naming-container">
						<table id="processChoiceTable">
							<tr>
								<td>
					                <p:outputLabel for="processChoice" value="Process Types"/>
					            </td>
					            <td>
					                <p:selectOneMenu id="processChoice" value="#{processesController.selectedProcessType}">
						                <f:selectItem itemLabel="Select Process Type" itemValue=""/>
						                <f:selectItems value="#{processesController.getProcessChoices()}"/>
	   	                             	<p:ajax update="@form:processShowCard" event="itemSelect"
									        listener="#{processesController.selectedProcessTypeListener}"/>
				    	        	</p:selectOneMenu>
				    	    	</td>
				    		</tr>			                								
			            </table>
						</p:outputPanel>

						<br/><br/>

		                <p:outputPanel rendered="#{processesController.selectedProcessType != null}" id="processTableAndTimelinePanel">

							<p:outputPanel id="selectedProcessTypeInfoHeading">
								<h3>#{processesController.selectedProcessTypeInfo}</h3>
							</p:outputPanel>

							<!-- Display table of process steps for selected type -->
							<p:outputPanel id="processTablePanel">
							<p:dataTable id="processTable" var="process"
										 value="#{processesController.processesForSelectedType}"
										 rendered="#{not empty processesController.processesForSelectedType}"
										 style="margin-bottom:20px; width: 100%" reflow="true" stripedRows="true"
										 editable="true" widgetVar="processTableWidget">

								<!-- AJAX event listeners for editing and cancelling row edits -->
								<!-- Hybrid approach: minimal XHTML update + programmatic updates -->
								<p:ajax event="rowEdit" listener="#{processesController.onRowEdit}"
								        update="@this" />

								<p:ajax event="rowEditCancel" listener="#{processesController.onRowCancel}"/>

								<f:facet name="header">
									<h:outputText value="Process steps for #{processesController.selectedProcessType} type"
												  style="text-align: center; display: block; font-size: 1.2rem;"/>
								</f:facet>

								<p:column headerText="Order Number">
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{process.orderNumber}"/>
										</f:facet>
										<f:facet name="input">
											<p:inputText value="#{process.orderNumber}" style="width: 100%"/>
										</f:facet>
									</p:cellEditor>
								</p:column>

								<p:column headerText="Process Step Name">
									<p:cellEditor>
										<f:facet name="output">
											<h:outputText value="#{process.name}"/>
										</f:facet>
										<f:facet name="input">
											<p:inputText value="#{process.name}" style="width: 100%"/>
										</f:facet>
									</p:cellEditor>
								</p:column>

								<p:column headerText="Jet Size">
									<h:outputText value="#{process.jetSizeKind}"/>
								</p:column>

								<p:column headerText="Process Type">
									<h:outputText value="#{process.processType}"/>
								</p:column>

								<!-- Row Editor Column -->
								<p:column headerText="Edit" style="width:80px">
									<p:rowEditor editTitle="Edit Row" cancelTitle="Cancel Edit" saveTitle="Save Row"/>
								</p:column>

							</p:dataTable>
							</p:outputPanel>

			                <br/><br/>

							<h3>Interactive Process Timeline</h3>
							<!-- Creates and displays an interactive timeline of process steps for selected type -->
							<p:outputPanel id="timelinePanel">
							<div class="custom-timeline" id="timelineContainer">
								<ui:repeat value="#{processesController.chosenProcessSteps}" var="eachStep">

									<!-- Dynamic CSS class for right/left alignment -->
									<div class="timeline-step #{(eachStep.orderNumber % 2 == 1) ? 'chronoline-step-right' : 'chronoline-step-left'}">

										<!-- Step header -->
										<div class="step-name"><h4>#{eachStep.name}</h4></div>

										<!-- Checkboxes container with proper alignment-->
										<div class="checkbox-container">

											<!-- Merge Checkbox -->
											<p:outputPanel rendered="#{eachStep.canMergePrevious}">
												<!--	Merge Happens lower than #{eachStep.mergeBelowBucketRate} bucket rate -->
												<p:selectBooleanCheckbox id="mergeCheck_#{eachStep.stepId}"
																		 value="#{eachStep.doMerge}"
																		 itemLabel="Merge with previous step">
													<p:ajax listener="#{processesController.setMerge(eachStep)}"
															process="@this"
															update="@form:timelinePanel" />
												</p:selectBooleanCheckbox>
											</p:outputPanel>

											<!-- Duplicate Checkbox -->
											<p:outputPanel rendered="#{eachStep.canDuplicate}">
												<!--	Duplicate Happens above #{eachStep.duplicateAboveBucketRate} bucket rate -->
												<p:selectBooleanCheckbox id="duplicateCheck_#{eachStep.stepId}"
																		 value="#{eachStep.doDuplicate}"
																		 itemLabel="Duplicate this step">
													<p:ajax listener="#{processesController.setDuplicate(eachStep)}"
															process="@this"
															update="@form:timelinePanel" />
												</p:selectBooleanCheckbox>
											</p:outputPanel>

											<!-- Delete Checkbox -->
											<p:outputPanel rendered="#{eachStep.canDelete}">
												<p:selectBooleanCheckbox id="deleteCheck_#{eachStep.stepId}"
																		 value="#{eachStep.doDelete}"
																		 itemLabel="Delete this step">
													<p:ajax listener="#{processesController.setDelete(eachStep)}"
															process="@this"
															update="@form:timelinePanel" />
												</p:selectBooleanCheckbox>
											</p:outputPanel>
										</div>
									</div>
								</ui:repeat>
							</div>
							</p:outputPanel>

						</p:outputPanel>

					</p:card>

				</div>														

			</div>

			<!-- Confirmation dialogs for actions: Merge, Duplicate, Delete -->
			<f:subview id="confirmationDialogsGroup">
			<!-- Merge Confirmation Dialog -->
			<p:dialog id="mergeConfirmDialog" widgetVar="mergeConfirmDialog"
					  header="Confirm Merge" responsive="true" width="500" modal="true"
					  onShow="bindDialogKeyEvents('mergeConfirmDialog', 'ui-button-warning', 'ui-button-secondary')"
					  onHide="unbindDialogKeyEvents()">
			<h:outputText value="Are you sure you want to MERGE this step with the previous one?" />
				<br/><br/>
				<h:panelGrid columns="2" cellpadding="5">
					<h:outputText value="Current Step:" style="font-weight: bold;" />
					<h:outputText value="#{processesController.pendingActionStep.name}" />

					<h:outputText value="Will merge with:" style="font-weight: bold;" />
					<h:outputText value="#{processesController.mergeTargetStepName}" />

					<h:outputText value="New (merged) Step Name:" style="font-weight: bold;" />
					<p:inputText value="#{processesController.dialogNewStepName}" style="width: 350px;" />
				</h:panelGrid>
				<br/>
				<h:outputText value="This action cannot be undone." style="color: red;" />

				<f:facet name="footer">
					<p:commandButton value="Cancel"
									 action="#{processesController.cancelMerge()}"
									 oncomplete="PF('mergeConfirmDialog').hide()"
									 update="@form:timelinePanel"
									 styleClass="ui-button-secondary" />
					<p:commandButton value="Merge"
									 action="#{processesController.executeMerge()}"
									 oncomplete="PF('mergeConfirmDialog').hide()"
									 update="@form:timelinePanel @form:processTablePanel @form:selectedProcessTypeInfoHeading"
									 styleClass="ui-button-warning" />
				</f:facet>
			</p:dialog>

			<!-- Duplicate Confirmation Dialog -->
			<p:dialog id="duplicateConfirmDialog" widgetVar="duplicateConfirmDialog"
					  header="Confirm Duplication" responsive="true" width="500" modal="true"
					  onShow="bindDialogKeyEvents('duplicateConfirmDialog', 'ui-button-success', 'ui-button-secondary')"
					  onHide="unbindDialogKeyEvents()">

			<h:panelGrid columns="2" cellpadding="5">
					<h:outputText value="Original Step Name:" style="font-weight: bold;" />
					<p:inputText value="#{processesController.originalStepName}" style="width: 350px;"/>

					<h:outputText value="New Step Name:" style="font-weight: bold;" />
					<p:inputText value="#{processesController.newStepName}" style="width: 350px;" />
				</h:panelGrid>

				<f:facet name="footer">
					<p:commandButton value="Cancel"
									 action="#{processesController.cancelDuplicate()}"
									 oncomplete="PF('duplicateConfirmDialog').hide()"
									 update="@form:timelinePanel"
									 styleClass="ui-button-secondary" />
					<p:commandButton value="Duplicate"
									 action="#{processesController.executeDuplicate()}"
									 oncomplete="PF('duplicateConfirmDialog').hide()"
									 update="@form:timelinePanel @form:processTablePanel @form:selectedProcessTypeInfoHeading"
									 styleClass="ui-button-success" />
				</f:facet>
			</p:dialog>

			<!-- Delete Confirmation Dialog -->
			<p:dialog id="deleteConfirmDialog" widgetVar="deleteConfirmDialog"
					  header="Confirm Deletion" responsive="true" width="400" modal="true"
					  onShow="bindDialogKeyEvents('deleteConfirmDialog', 'ui-button-danger', 'ui-button-secondary')"
					  onHide="unbindDialogKeyEvents()">

			<h:outputText value="Are you sure you want to DELETE this process step?" />
				<br/><br/>
				<h:outputText value="Step: " style="font-weight: bold;" />
				<h:outputText value="#{processesController.getPendingActionStep() != null ? processesController.getPendingActionStep().getName() : 'N/A'}" />
				<br/><br/>
				<h:outputText value="This action cannot be undone." style="color: red;" />

				<f:facet name="footer">
					<p:commandButton value="Cancel"
									 action="#{processesController.cancelDelete()}"
									 oncomplete="PF('deleteConfirmDialog').hide()"
									 update="@form:timelinePanel"
									 styleClass="ui-button-secondary" />
					<p:commandButton value="Delete"
									 action="#{processesController.executeDelete()}"
									 oncomplete="PF('deleteConfirmDialog').hide()"
									 update="@form:timelinePanel @form:processTablePanel @form:selectedProcessTypeInfoHeading"
									 styleClass="ui-button-danger" />
				</f:facet>
			</p:dialog>
			</f:subview>

		</h:form>

	</ui:define>

</ui:composition>