quarkus.application.name = unite
%prod.quarkus.banner.path = banner.txt
%test.quarkus.banner.path = banner-test.txt
%dev.quarkus.banner.path = banner-dev.txt
%m3cloud.quarkus.banner.path = banner-m3cloud.txt

quarkus.http.root-path = /
# for prod (on Server)
%test.quarkus.http.host = 0.0.0.0
%prod.quarkus.http.host = 0.0.0.0
%m3cloud.quarkus.http.host = 0.0.0.0
## change this to bind to all interfaces (0.0.0.0) instead of just localhost, to enable access to WSL
%dev.quarkus.http.host = 0.0.0.0
%test.quarkus.http.port = 5001
%prod.quarkus.http.port = 5000
%dev.quarkus.http.port = 5002
%m3cloud.quarkus.http.port = 5003

#allow the code to call JNDI (to allow logon)
# - - Change - -
quarkus.naming.enable-jndi=true

# - - Window - -
#%dev.quarkus.http.ssl.certificate.key-store-file="C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\lib\security\keystore.jks"
# - - Linux AND Quarkus - -
#%dev.quarkus.http.ssl.certificate.key-store-file="C:/Program Files/Eclipse Adoptium/jdk-*********-hotspot/lib/security/keystore.jks"

#quarkus.http.ssl.certificate.key-store-file=C:/Temp/keystore/keystore-d.jks

# - - Change - -
#quarkus.http.ssl.certificate.trust-store-file=/etc/ssl/certs/java/cacerts
#quarkus.http.ssl.certificate.trust-store-file=C:\Temp\keystore\cacerts-d
#quarkus.http.ssl.certificate.trust-store-password=changeit

#quarkus.http.ssl.certificate.key-store-file=C:/temp/wildcard_hamjet_co_nz-expiresdec2021.pfx

#%dev.quarkus.http.ssl.certificate.key-store-file-type=
#quarkus.http.ssl.certificate.key-store-file-type=PFX
#quarkus.http.ssl.certificate.key-store-file-type=JKS

#%dev.quarkus.http.ssl.certificate.key-store-password=
#quarkus.http.ssl.certificate.key-store-password=temppassword12345
#quarkus.http.ssl.certificate.key-store-password=changeit

#%dev.quarkus.http.insecure-requests=
#quarkus.http.insecure-requests=redirect

# Hibernate/JPA cache settings:
# Disables second-level caching entirely
quarkus.hibernate-orm.second-level-caching-enabled=false

### DB MSSQL
quarkus.datasource.db-kind=mssql
quarkus.datasource.jdbc.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
#quarkus.datasource.jdbc.driver=com.mysql.cj.jdbc.Driver

# - - - DB Source - - -
%prod.quarkus.datasource.username=svcSQL-unite
%test.quarkus.datasource.username=svcSQL-unite-test
%dev.quarkus.datasource.username=svcSQL-unite-dev
%m3cloud.quarkus.datasource.username=svcSQL-unite-M3cloud

# - - Quarkus Datasource Password - -
%prod.quarkus.datasource.password=tdQB7h6BxADS26qC
%test.quarkus.datasource.password=70371BA6-D6DF-44C2-B93F-F6EE0F03EA6D
%dev.quarkus.datasource.password=15FCC7E9-3919-40CC-B12F-E316FD7232D8
%m3cloud.quarkus.datasource.password=Uvhjvhygygg37gsgsdhvhdsvdvjhavds

# - - DB - ->
%prod.quarkus.datasource.jdbc.url=************************************************************************************************************;
%test.quarkus.datasource.jdbc.url=************************************************************************************************************;
%dev.quarkus.datasource.jdbc.url=***********************************************************************************************************;
%m3prod.quarkus.datasource.jdbc.url=***************************************************************************************************************;
# <- - DB - -

# - - Change - -
#quarkus.hibernate-orm.log.sql=true
#quarkus.hibernate-orm.log.format-sql=true
#quarkus.hibernate-orm.log.bind-parameters=true
#quarkus.log.category."org.hibernate.SQL".level=DEBUG
#quarkus.log.category."org.hibernate.type.descriptor.sql".level=TRACE

# drop and create the database at startup (use `update` to only update the schema and drop-and-create)
quarkus.hibernate-orm.database.generation=update
#quarkus.hibernate-orm.database.generation=drop-and-create
#quarkus.hibernate-orm.sql-load-script=import.sql

# dont reload on body changes
# quarkus.dev.instrumentation=true
quarkus.live-reload.instrumentation=true

quarkus.log.console.enable = true

# - - Change reverted - -
#%dev.quarkus.log.console.color = false

quarkus.container-image.jvm-args=-Duser.timezone=Pacific/Auckland
quarkus.log.console.format = %z{Pacific/Auckland}%d{yyyy-MM-dd HH\:mm\:ss,SSS} %-5p [%c{3.}] (%t) %s%e%n
#quarkus.log.console.level = FINE
quarkus.log.category."jakarta.faces.validator.BeanValidator".level=ERROR

quarkus.log.file.enable = true
quarkus.log.file.format = %z{Pacific/Auckland}%d{yyyy-MM-dd HH\:mm\:ss,SSS} %-5p [%c{3.}] (%t) %s%e%n
quarkus.log.file.path = logs/app.log
quarkus.log.file.rotation.max-file-size = 10M
quarkus.log.file.rotation.max-backup-index = 10
quarkus.log.file.rotation.rotate-on-boot = true
quarkus.servlet.max-parameters=5000


# - - Change - -
#quarkus.oidc-client.auth-server = https://mingletest.hamjet.co.nz/InforIntSTS/connect/token
#quarkus.oidc-client.client-id = infor~cxLEMltdHAo0u4YNdkr8_zdvTgZz3OE5OXhM8-KSN3E
#quarkus.oidc-client.credentials.secret = nTNUTSORGHMbhATFk3OLIVSxzEGViO-sn4xfkOrOVvXej6r8pr1YDEx9TFgSZ2G6YePCZnTAwhPW7UeQfk8VkA
#oauth2.username = infor#0_8eLiIJSQpNiNSKUnw-rKd43-mIPs0Hrkwb9-HGYC6Gmu1dI40A4b-G2TJc92yUu6BTlsk3jfmJ0bN0fWtchg
#oauth2.password = 31lgBdQtZPOk8n88_TH01Kwi3u8UywwiSMfB_DNTkd1lJDgtRCQIvSHDagIvny_HN58ZJ-7xBIq3YHPBAdO_0A
#oauth2.scope = openid profile
# - - copy of the above
#quarkus.oidc-client.auth-server = https://mingletest.hamjet.co.nz/InforIntSTS/connect/token
#quarkus.oidc-client.client-id = infor~cxLEMltdHAo0u4YNdkr8_zdvTgZz3OE5OXhM8-KSN3E
#quarkus.oidc-client.credentials.secret = nTNUTSORGHMbhATFk3OLIVSxzEGViO-sn4xfkOrOVvXej6r8pr1YDEx9TFgSZ2G6YePCZnTAwhPW7UeQfk8VkA
#oauth2.username = infor#0_8eLiIJSQpNiNSKUnw-rKd43-mIPs0Hrkwb9-HGYC6Gmu1dI40A4b-G2TJc92yUu6BTlsk3jfmJ0bN0fWtchg
#oauth2.password = 31lgBdQtZPOk8n88_TH01Kwi3u8UywwiSMfB_DNTkd1lJDgtRCQIvSHDagIvny_HN58ZJ-7xBIq3YHPBAdO_0A
#oauth2.scope = openid profile

# MyFaces configuration
%dev.quarkus.servlet.context-path=/

# --- M3 Service Configuration ---
# Set to true to log the raw JSON response from M3 API calls for debugging
# Default is false if not specified
unite.m3.log-raw-response=false
# For development, you might want to enable it by default:
#%dev.unite.m3.log-raw-response=true

## Scheduler jobs configuration
# for recalculating schedule numbers (every 2 hrs on work days: 5am, 7am, 9am, ...)
%dev.recalculate.schedule.numbers.cron=off
%test.recalculate.schedule.numbers.cron=off
%prod.recalculate.schedule.numbers.cron=0 0 5/2 ? * MON-FRI
recalculate.schedule.numbers.timezone=Pacific/Auckland


# Debug logging
quarkus.log.category."unite.nz.hamiltonjet.entity.PlannerLine".min-level=TRACE
quarkus.log.category."unite.nz.hamiltonjet".min-level=TRACE
#%dev.quarkus.log.category."io.quarkus".level=DEBUG
#%dev.quarkus.log.category."org.apache.myfaces".level=DEBUG
%dev.quarkus.log.category."unite.nz.hamiltonjet".level=DEBUG
%dev.quarkus.log.category."unite.nz.hamiltonjet.utils.LoggingEventListener".level=INFO
%dev.quarkus.log.category."unite.nz.hamiltonjet.controllers.security.interceptors".level=WARN
#%dev.quarkus.log.category."unite.nz.hamiltonjet.service.m3.ApiService".level=TRACE
%dev.quarkus.log.category."unite.nz.hamiltonjet.entity.PlannerLine".level=TRACE
%test.quarkus.log.category."unite.nz.hamiltonjet.entity.PlannerLine".level=TRACE
%dev.quarkus.log.category."unite.nz.hamiltonjet.entity.enums.ProcessType".level=TRACE
%test.quarkus.log.category."unite.nz.hamiltonjet.entity.enums.ProcessType".level=TRACE
%dev.quarkus.log.category."unite.nz.hamiltonjet.service.process".level=TRACE
%dev.quarkus.log.category."unite.nz.hamiltonjet.service.data.PlannerService".level=TRACE
%test.quarkus.log.category."unite.nz.hamiltonjet.service.data.PlannerService".level=TRACE
%dev.quarkus.log.category."unite.nz.hamiltonjet.service.data.BasketsService".level=TRACE
%test.quarkus.log.category."unite.nz.hamiltonjet.service.data.BasketsService".level=TRACE
%dev.quarkus.log.category."unite.nz.hamiltonjet.service.m3.M3DataStoreService".level=TRACE
%test.quarkus.log.category."unite.nz.hamiltonjet.service.m3.M3DataStoreService".level=TRACE
%dev.quarkus.log.category."unite.nz.hamiltonjet.service.m3.M3TokenService".level=DEBUG
%dev.quarkus.log.category."unite.nz.hamiltonjet.controllers.baskets".level=TRACE
%dev.quarkus.log.category."unite.nz.hamiltonjet.controllers.planner.slotboards".level=TRACE
%dev.quarkus.log.category."unite.nz.hamiltonjet.controllers.weeks".level=DEBUG
%dev.quarkus.log.category."unite.nz.hamiltonjet.exception".level=INFO

